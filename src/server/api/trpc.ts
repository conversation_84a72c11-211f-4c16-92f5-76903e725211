/**
 * YOU PROBABLY DON'T NEED TO EDIT THIS FILE, UNLESS:
 * 1. You want to modify request context (see Part 1).
 * 2. You want to create a new middleware or type of procedure (see Part 3).
 *
 * TL;DR - This is where all the tRPC server stuff is created and plugged in. The pieces you will
 * need to use are documented accordingly near the end.
 */
import OAuthClient from "intuit-oauth";
import * as qbo from "qbo";
import { encode, getToken, type JWT } from "next-auth/jwt";

import { initTRPC, TRPCError } from "@trpc/server";
import superjson from "superjson";
import { ZodError } from "zod";
import { env } from "@/env";

import { auth } from "@/server/auth";
import { db } from "@/server/db";
import type { NextRequest, NextResponse } from "next/server";
import type { QbAuthInfo } from "@/types/company";

/**
 * 1. CONTEXT
 *
 * This section defines the "contexts" that are available in the backend API.
 *
 * These allow you to access things when processing a request, like the database, the session, etc.
 *
 * This helper generates the "internals" for a tRPC context. The API handler and RSC clients each
 * wrap this and provides the required context.
 *
 * @see https://trpc.io/docs/server/context
 */
export const createTRPCContext = async (opts: {
  headers: Headers;
  url: string;
  // req: NextRequest;
  // res: NextResponse;
}) => {
  const session = await auth();

  return {
    db,
    session,
    ...opts,
  };
};

/**
 * 2. INITIALIZATION
 *
 * This is where the tRPC API is initialized, connecting the context and transformer. We also parse
 * ZodErrors so that you get typesafety on the frontend if your procedure fails due to validation
 * errors on the backend.
 */
const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
  // Configure SSE for subscriptions
  sse: {
    // Send ping messages to keep the connection alive
    ping: {
      enabled: true,
      intervalMs: 3000, // 3 seconds - must be less than reconnectAfterInactivityMs
    },
    // Client configuration
    client: {
      // Reconnect after inactivity
      reconnectAfterInactivityMs: 10000, // 10 seconds
    },
  },
});

/**
 * Create a server-side caller.
 *
 * @see https://trpc.io/docs/server/server-side-calls
 */
export const createCallerFactory = t.createCallerFactory;

/**
 * 3. ROUTER & PROCEDURE (THE IMPORTANT BIT)
 *
 * These are the pieces you use to build your tRPC API. You should import these a lot in the
 * "/src/server/api/routers" directory.
 */

/**
 * This is how you create new routers and sub-routers in your tRPC API.
 *
 * @see https://trpc.io/docs/router
 */
export const createTRPCRouter = t.router;

const qbAuthMiddleware = t.middleware(async ({ ctx, next, input }) => {
  // Create OAuth client with environment variables
  const userCompanies = await ctx.db.userCompany.findMany({
    where: {
      userId: ctx.session?.user.userId,
    },
    include: {
      Company: true,
    },
  });
  if (
    !userCompanies ||
    !userCompanies.length ||
    !userCompanies[0]?.Company?.authInfo
  ) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  const tokenInfo = userCompanies[0].Company.authInfo as Record<string, any>;
  const { createdAt, ...restTokenInfo } = tokenInfo;
  const oauthClient = new OAuthClient({
    clientId: env.QB_CLIENT_ID,
    clientSecret: env.QB_CLIENT_SECRET,
    environment: "production",
    redirectUri: "http://localhost:3000/boarding",
    token: {
      ...(restTokenInfo as any),
    },
    logging: false,
  });
  const now = new Date();
  const inputDate = new Date(tokenInfo?.createdAt || "2000-08-07");

  if (now.getTime() - inputDate.getTime() > 55 * 60 * 1000) {
    const newToken = await oauthClient.refresh();
    await ctx.db.qbCompany.update({
      where: {
        companyId: userCompanies[0].companyId,
      },
      data: {
        authInfo: { ...newToken.json, createdAt: new Date() },
      },
    });
  }

  const res = next({
    ctx: {
      ...ctx,
      qb: oauthClient,
    },
  });

  return res;
});
export const qbAuthMiddlewareV2 = t.middleware(async ({ ctx, next, input }) => {
  // Create OAuth client with environment variables

  const inputData = input as any;

  if (!(("companyId" in inputData) as any)) {
    throw new TRPCError({ code: "BAD_REQUEST" });
  }
  const userCompanies = await ctx.db.userCompany.findMany({
    where: {
      // OR: [
      //   {
      //     companyId: inputData?.companyId,
      //   },
      //   {
      //     userId: ctx.session?.user.userId,
      //   },
      // ],
      userId: ctx.session?.user.userId,
      companyId: inputData?.companyId,
    },
    include: {
      Company: true,
    },
  });
  console.log(userCompanies);

  if (!userCompanies || !userCompanies?.length || !userCompanies?.[0]) {
    console.log("no company found");
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  let tokenInfo = userCompanies?.find(
    (uc) => uc.companyId === inputData?.companyId,
  )?.Company?.authInfo as QbAuthInfo;

  const { createdAt, ...restTokenInfo } = tokenInfo;
  const oauthClient = new OAuthClient({
    clientId: env.QB_CLIENT_ID,
    clientSecret: env.QB_CLIENT_SECRET,
    environment: "production",
    // environment: "sandbox",
    redirectUri: "http://localhost:3000/boarding",
    token: {
      ...(restTokenInfo as any),
    },
    logging: false, // qb logs
  });
  console.log(oauthClient);

  const now = new Date();
  const inputDate = new Date(tokenInfo?.createdAt || "2000-08-07");

  if (now.getTime() - inputDate.getTime() > 55 * 60 * 1000) {
    console.log("refreshing token");
    const newToken = await oauthClient.refresh();
    console.log("refreshed token: ", newToken);
    const updatedCo = await ctx.db.qbCompany.update({
      where: {
        companyId: inputData?.companyId,
      },
      data: {
        authInfo: { ...newToken.json, createdAt: new Date() },
      },
    });
    tokenInfo = updatedCo.authInfo as QbAuthInfo;
  }

  const res = next({
    ctx: {
      ...ctx,

      qb: oauthClient,
      realm_id: userCompanies?.find(
        (uc) => uc.companyId === inputData?.companyId,
      )?.Company?.realmId,
    },
  });

  return res;
});

/**
 * Public (unauthenticated) procedure
 *
 * This is the base piece you use to build new queries and mutations on your tRPC API. It does not
 * guarantee that a user querying is authorized, but you can still access user session data if they
 * are logged in.
 */
export const publicProcedure = t.procedure;
export const cbProcedure = t.procedure.use(
  ({ ctx, getRawInput, input, next, type, meta, path, signal }) => {
    return next({
      ctx: {
        ...ctx,
      },
    });
  },
);

/**
 * Protected (authenticated) procedure
 *
 * If you want a query or mutation to ONLY be accessible to logged in users, use this. It verifies
 * the session is valid and guarantees `ctx.session.user` is not null.
 *
 * @see https://trpc.io/docs/procedures
 */
export const protectedProcedure = t.procedure.use(({ ctx, next }) => {
  const paramsFromURL = new URLSearchParams(ctx.url.split("?")[1]);

  if (!ctx.session?.user) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }
  return next({
    ctx: {
      // infers the `session` as non-nullable
      session: { ...ctx.session, user: ctx.session.user },
    },
  });
});

export const qbProcedure = t.procedure.use(({ ctx, next }) => {
  console.log("dom dom dom sa7or");
  if (!ctx.session?.user) {
    console.log("noooooo laaa politziaaa");

    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  return next({
    ctx: {
      session: {
        ...ctx.session,
        user: { ...ctx.session.user },
      },
    },
  });
});
