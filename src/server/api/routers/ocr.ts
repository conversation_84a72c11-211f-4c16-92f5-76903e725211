import { z } from "zod";
import { TRPCError } from "@trpc/server";
import {
  createTRPCRouter,
  protectedProcedure,
  qbAuthMiddlewareV2,
  qbProcedure,
} from "@/server/api/trpc";
import { GoogleGenAI, Type } from "@google/genai";
import FormData from "form-data";
import { Buffer } from "buffer";
import fetch from "node-fetch";

import { env } from "@/env";

import { similaritySearch } from "@/helpers/ner";
import type { TaxCodeI } from "@/types/taxCode";
import type { CreateBillI } from "@/types/craeteBill";
function extractMimeTypeAndBase64(dataUri: string) {
  try {
    const matches = dataUri.match(
      /^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,(.*)$/,
    );

    if (!matches || matches.length !== 3) {
      return { mimeType: null, base64: null }; // Or throw an error, depending on your needs.
    }

    const mimeType = matches[1];
    const base64 = matches[2];

    return { mimeType: mimeType, base64: base64 };
  } catch (error) {
    console.error("Error extracting mime type and base64:", error);
    return { mimeType: null, base64: null }; // Or re-throw the error if appropriate.
  }
}
// const ee = new EventEmitter();
export const ocrRouter = createTRPCRouter({
  ocr: qbProcedure
    .input(z.object({ companyId: z.string(), base64: z.string() }))
    .use(qbAuthMiddlewareV2)
    .mutation(async ({ ctx, input }) => {
      const imageConfig = extractMimeTypeAndBase64(input.base64 || "");

      const ai = new GoogleGenAI({
        apiKey: env.GEMINI_API_KEY,
      });
      const config = {
        systemInstruction: [
          {
            text: `you are an agent responsible for extracting information from bills.
                if the user provided an image of a bill return the key is_bill true
                and extract the vendor name and the list of the items names only.
                don't add any other information.
                don't make any assumptions.
                if you cant extract the information return is_bill false.
                if the image is not a bill return is_bill false.
                `,
          },
        ],

        responseMimeType: "application/json",
        responseSchema: {
          type: Type.OBJECT,
          required: ["vendorName"],
          properties: {
            is_bill: {
              type: Type.BOOLEAN,
            },
            vendorName: {
              type: Type.STRING,
            },
            ItemsNames: {
              type: Type.ARRAY,
              items: {
                type: Type.STRING,
              },
            },
          },
        },
      };

      const model = "gemini-2.0-flash";
      const contents = [
        {
          role: "user",
          parts: [
            {
              inlineData: {
                data: imageConfig.base64 || "",
                mimeType: imageConfig.mimeType || "",
              },
            },
          ],
        },
      ];
      const response = await ai.models.generateContent({
        model,
        config,
        contents,
      });
      const ocrResponse = JSON.parse(response.text || '{"is_bill": false}') as {
        vendorName: string;
        ItemsNames: string[];
        is_bill: boolean;
      };
      if (ocrResponse.is_bill) {
        const vendors = await ctx.db.qbVendor.findMany({
          where: {
            companyId: input.companyId,
          },
          select: {
            DisplayName: true,
            Id: true,
          },
        });
        const vendorsPool = vendors.map((v) => ({
          search_term: v?.DisplayName || "",
          query_term: v?.Id || "",
        }));

        const vendorSimilarityResult = await similaritySearch(
          ocrResponse.vendorName,
          {
            vendors: vendorsPool,
          },
        );

        // items Part
        const items = await ctx.db.qbItem.findMany({
          where: {
            companyId: input.companyId,
          },
          select: {
            Name: true,
            Id: true,
          },
        });

        const itemsPool = items.map((i) => ({
          search_term: i?.Name || "",
          query_term: i?.Id || "",
        }));
        let itemsSimilarityResult: any[] = [];
        for (const itemName of ocrResponse.ItemsNames) {
          const itemSimilarityResult = await similaritySearch(itemName, {
            items: itemsPool,
          });
          itemsSimilarityResult.push(itemSimilarityResult);
        }

        const taxRates = await ctx.db.qbTaxRate.findMany({
          where: {
            companyId: input.companyId,
          },
          select: {
            Name: true,
            RateValue: true,
            Id: true,
          },
        });
        const taxCodes = await ctx.db.qbTaxCode.findMany({
          where: {
            companyId: input.companyId,
          },
          select: {
            Name: true,
            Id: true,
            PurchaseTaxRateList: true,
          },
        });
        const terms = await ctx.db.qbTerm.findMany({
          where: {
            companyId: input.companyId,
          },
          select: {
            Name: true,
            DueDays: true,
            Type: true,
            DayOfMonthDue: true,
            Id: true,
          },
        });

        const taxes = taxCodes
          .filter(
            (tc) =>
              (tc.PurchaseTaxRateList as TaxCodeI["PurchaseTaxRateList"])
                ?.TaxRateDetail?.length,
          )
          .map((tc) => {
            const taxRate = (
              tc.PurchaseTaxRateList as TaxCodeI["PurchaseTaxRateList"]
            )?.TaxRateDetail?.reduce((acc, curr) => {
              const taxRateRefId = curr.TaxRateRef?.value;
              const taxRate = taxRates.find((tr) => tr.Id === taxRateRefId);
              if (taxRate) {
                acc += taxRate.RateValue || 0;
              }
              return acc;
            }, 0);
            return {
              name: tc.Name,
              id: tc.Id,
              rate: taxRate,
            };
          });

        const finalBillPrompt = {
          text: `
        You are an assistant helping to generate a QuickBooks-compatible bill object.
        
        Since in QuickBooks you need Ids to create a bill, you can find bellow the lookups you need:
        - Matched Vendors: ${JSON.stringify(
          vendorSimilarityResult.vendors.map((item: any) => ({
            vendor_name: item.search_term,
            id: item.query_term,
          })),
        )}
        - Matched Items:
        ${JSON.stringify(
          itemsSimilarityResult.flatMap((entry) =>
            entry.items.map((item: any) => ({
              item_name: item.search_term,
              id: item.query_term,
            })),
          ),
        )}
        - Terms: ${JSON.stringify(terms)}
        - Tax Rates: ${JSON.stringify(taxes)}
        
       output:
        the output will be a json object with this structure:
        {
          "SalesTermRef": {
              "value": "sales term id"
          },
          "Line": [
              {
                  "DetailType": "ItemBasedExpenseLineDetail", // don't change this
                  "Description": "the item name from the bill image", // the name of the item if it exists
                  "Amount": the total amount of the item (from the bill image) its the (qty * the unit price),
                  "ItemBasedExpenseLineDetail": {
                      "ItemRef": {
                          "value": "the id of the item (use the id from the matched items)",
                      },
                      "UnitPrice": the price per unit of the item (from the bill image),
                      "Qty": the quantity of the item (from the bill image),
                      "TaxCodeRef": {
                          "value": "the tax rate id (use the id from the matched tax rates based on whats written in the bill image)"
                      }
                  }
              },
              ... the rest of the items
          ],
          "PrivateNote": "if there is a note in the bill image, extract it here",
          "VendorRef": {
              "value": "the id of the vendor (use the id from the matched vendors)", 
          },
          "TxnDate": "the date of the bill (from the bill image) format YYYY-MM-DD",
          "DueDate": "the due date of the bill (from the bill image) format YYYY-MM-DD"
        }

        Rules:
          ItemRef:
            1. use the ids and the names from the items lookup list
            2. if there is an item in the bill and its not in the lookup list, only add a "name" key and use its name in the bill and remove the "value" key inside the "ItemRef"
          TaxCodeRef:
            1. use the ids from the tax rates lookup list
            2. if there is no tax rate in the bill, don't add the "TaxCodeRef" key
            3. this is a bill so use the tax rates for bills (purchase rates), the names of the rates are shorts name used by accountants so figure it out.
            4. if there is tax add 	"GlobalTaxCalculation": "TaxExcluded" to the root of the json object.
          VendorRef:
            1. use the ids and the names from the vendors lookup list
            2. if there is a vendor in the bill but its not in the lookup list, only add a "name" and use its name in the bill and remove the "value" key inside the "VendorRef"
          PrivateNote:
            1. if there is a Tax Invoice Number in the bill, add it to the "PrivateNote" key as "Tax Invoice Number: <the number>"
            2. at the end of the "PrivateNote" add "Generated by Wilfredo at ${new Date()}".
          Sales term & dates
            TxnDate:
              1. the date of the bill is mentioned in the image, extract it
              2. format the date as YYYY-MM-DD  
            SalesTermRef:
              1. use the ids from the terms lookup list
              2. if there is no term in the bill, don't add the "SalesTermRef" key
              3. if there is a term in the bill, use the id from the terms lookup list
              4. if there is a due date in the bill, use the best suited term that matches the due date
            DueDate:
              1. if there is a term and no due date, calculate the due date based on the term definition. 
              2. if the due date is mentioned in the image, extract it.
              3. format the date as YYYY-MM-DD.
              4. if the term says (n) days from end of the month, calculate the due date based on the last day of the month.


          Notes:
            - if an item have not QTY and no price, ignore the item. (example: some bills have a discount row with the items ignore it).
            - the dates in the bills will be in the format of dd-mm-yyyy or yyyy-mm-dd cuz the businesses are in the UAE.
            - Always return a valid json object.
            - No Markdown decorations.
            - No explanations.
          
        `,
        };

        const secondCall = await ai.models.generateContent({
          model,
          config: {
            systemInstruction: [finalBillPrompt],
            responseMimeType: "application/json",
          },
          contents: [
            {
              role: "user",
              parts: [
                {
                  inlineData: {
                    data: imageConfig.base64 || "",
                    mimeType: imageConfig.mimeType || "",
                  },
                },
                { text: "Generate the Bill JSON." },
              ],
            },
          ],
        });

        try {
          const bill = JSON.parse(secondCall?.text as any);
          if (!bill) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Failed to process bill",
            });
          }
          if (!bill?.Line?.length || !bill.VendorRef.value) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Failed to process bill, please provide a valid bill.",
            });
          }
          return {
            bill: bill as CreateBillI,
            taxes,
            vendors,
            items,
            terms,
          };
        } catch (e) {}
      } else {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to process bill, please provide a valid bill.",
        });
      }
    }),

  createAttachable: qbProcedure
    .input(z.object({ companyId: z.string(), base64: z.string() }))
    .use(qbAuthMiddlewareV2)
    .mutation(async ({ ctx, input }) => {
      const billId = "149";

      const { mimeType, base64: base64Data } = extractMimeTypeAndBase64(
        input.base64,
      );
      const extention = mimeType?.split("/")[1] || "";

      const buffer = Buffer.from(base64Data || "", "base64");
      const timeStamp = new Date().getTime();
      const fileName = `wilfredo-${timeStamp}.${extention}`;

      const form = new FormData();

      // 🟢 1. Attach file metadata with correct part name
      form.append(
        "file_metadata_01",
        JSON.stringify({
          FileName: fileName,
          ContentType: mimeType,
          AttachableRef: [
            {
              EntityRef: {
                type: "Bill",
                value: billId,
              },
            },
          ],
        }),
        {
          contentType: "application/json",
          filename: "metadata.json",
        },
      );

      // 🟢 2. Attach the actual file content with correct part name
      form.append("file_content_01", buffer, {
        filename: fileName,
        contentType: mimeType || "",
      });

      const res = await fetch(
        `https://sandbox-quickbooks.api.intuit.com/v3/company/${ctx.realm_id}/upload`,
        {
          method: "POST",
          headers: {
            ...form.getHeaders(),
            Authorization: `Bearer ${ctx.qb.token.access_token}`,
            Accept: "application/json",
          },
          body: form,
        },
      );

      const rawText = await res.text();

      try {
        const json = JSON.parse(rawText);
        console.log("Upload successful:", json);
        return json;
      } catch (err) {
        console.error("Raw (non-JSON) response:", rawText);
        throw new Error("Upload failed or unexpected response");
      }
    }),
  searchVendors: qbProcedure
    .input(z.object({ companyId: z.string() }))
    .use(qbAuthMiddlewareV2)
    .mutation(async ({ ctx, input }) => {
      // Dhofar Global
      // FUJAIRAH PLASTIC FACTORY
      // DHOFAR GLOBAL TRADING CO. L.L.C.
      // SAFCO
      // EMIRATES SNACK FOODS LLC
      const vendors = await ctx.db.qbVendor.findMany({
        where: {
          companyId: input.companyId,
        },
        select: {
          DisplayName: true,
          Id: true,
        },
      });
      const vendorsPool = vendors.map((v) => ({
        search_term: v?.DisplayName || "",
        query_term: v?.Id || "",
      }));

      const simiRes = await similaritySearch("LLC", {
        vendors: vendorsPool,
      });
      // console.log(vendors);
      return simiRes;
    }),
});
