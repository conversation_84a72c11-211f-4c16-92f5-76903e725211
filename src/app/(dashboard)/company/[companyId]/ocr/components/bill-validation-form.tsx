"use client";

import { useState } from "react";
import { CalendarIcon, Plus, Trash2 } from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import type { OcrResponseI } from "@/types/ocrResponse";

export default function BillValidationForm({
  ocrResponse,
}: {
  ocrResponse: OcrResponseI;
}) {
  const [billData, setBillData] = useState(ocrResponse.bill);
  const [txnDate, setTxnDate] = useState<Date>(
    new Date(ocrResponse?.bill?.TxnDate || ""),
  );
  const [dueDate, setDueDate] = useState<Date>(
    new Date(ocrResponse?.bill?.DueDate || ""),
  );

  type LineLevelField = "Description" | "Amount";
  type ItemDetailField = "ItemRef" | "UnitPrice" | "Qty" | "TaxCodeRef";

  type Field = LineLevelField | ItemDetailField;

  const updateLineItem = (
    index: number,
    field: Field,
    value: any, // we'll type this better below
  ) => {
    const newLines = [...(billData?.Line || [])];
    if (!newLines?.[index]) return;

    if (field === "Description" || field === "Amount") {
      (newLines[index] as any)[field] = value;
    } else {
      (newLines[index].ItemBasedExpenseLineDetail as any)[field] = value;
    }

    if (field === "Qty" || field === "UnitPrice") {
      const qty =
        field === "Qty"
          ? value
          : newLines?.[index]?.ItemBasedExpenseLineDetail?.Qty;
      const unitPrice =
        field === "UnitPrice"
          ? value
          : newLines?.[index]?.ItemBasedExpenseLineDetail?.UnitPrice;
      newLines[index].Amount = qty * unitPrice;
    }

    setBillData({ ...billData, Line: newLines });
  };
  const addLineItem = () => {
    const newLine = {
      DetailType: "ItemBasedExpenseLineDetail",
      Description: "",
      Amount: 0,
      ItemBasedExpenseLineDetail: {
        ItemRef: { value: "" },
        UnitPrice: 0,
        Qty: 1,
        TaxCodeRef: { value: "5" },
      },
    };
    setBillData({ ...billData, Line: [...(billData?.Line || []), newLine] });
  };

  const removeLineItem = (index: number) => {
    const newLines = billData?.Line?.filter((_, i) => i !== index);
    setBillData({ ...billData, Line: newLines });
  };

  const getTotalAmount = () => {
    return billData?.Line?.reduce((sum, line) => sum + (line?.Amount || 0), 0);
  };

  const handleSubmit = () => {
    const finalBillData = {
      ...billData,
      TxnDate: format(txnDate, "yyyy-MM-dd"),
      DueDate: format(dueDate, "yyyy-MM-dd"),
    };
    console.log("Submitting bill data:", finalBillData);
    // Here you would send the data to create the bill in QuickBooks
  };

  return (
    <div className="mx-auto max-w-4xl space-y-6 p-4">
      <Card>
        <CardHeader>
          <CardTitle>Validate Bill Information</CardTitle>
          <CardDescription>
            Review and edit the extracted bill details before creating in
            QuickBooks
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Vendor Selection */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="vendor">Vendor</Label>
              <Select
                value={billData?.VendorRef?.value}
                onValueChange={(value) =>
                  setBillData({ ...billData, VendorRef: { value } })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select vendor" />
                </SelectTrigger>
                <SelectContent>
                  {ocrResponse?.vendors?.map((vendor) => (
                    <SelectItem key={vendor.Id} value={vendor.Id}>
                      {vendor.DisplayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="terms">Payment Terms</Label>
              <Select
                value={billData?.SalesTermRef?.value}
                onValueChange={(value) =>
                  setBillData({ ...billData, SalesTermRef: { value } })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select terms" />
                </SelectTrigger>
                <SelectContent>
                  {ocrResponse?.terms?.map((term) => (
                    <SelectItem key={term.Id} value={term.Id}>
                      {term.Name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Transaction Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !txnDate && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {txnDate ? (
                      format(txnDate, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={txnDate}
                    onSelect={(date: any) => date && setTxnDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>Due Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dueDate && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate ? (
                      format(dueDate, "PPP")
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dueDate}
                    onSelect={(date: any) => date && setDueDate(date)}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Line Items */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-lg font-semibold">Line Items</Label>
              <Button onClick={addLineItem} size="sm" variant="outline">
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </div>

            {billData?.Line?.map((line, index) => (
              <Card key={index} className="p-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">Item {index + 1}</Label>
                    {(billData?.Line?.length || 0) > 1 && (
                      <Button
                        onClick={() => removeLineItem(index)}
                        size="sm"
                        variant="ghost"
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="space-y-3">
                    <div>
                      <Label htmlFor={`item-${index}`}>Item</Label>
                      <Select
                        value={line?.ItemBasedExpenseLineDetail?.ItemRef?.value}
                        onValueChange={(value) =>
                          updateLineItem(index, "ItemRef", { value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select item" />
                        </SelectTrigger>
                        <SelectContent>
                          {ocrResponse?.items?.map((item) => (
                            <SelectItem key={item.Id} value={item.Id}>
                              {item.Name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor={`description-${index}`}>
                        Description
                      </Label>
                      <Textarea
                        id={`description-${index}`}
                        value={line.Description}
                        onChange={(e) =>
                          updateLineItem(index, "Description", e.target.value)
                        }
                        className="min-h-[60px]"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
                      <div>
                        <Label htmlFor={`qty-${index}`}>Quantity</Label>
                        <Input
                          id={`qty-${index}`}
                          type="number"
                          value={line?.ItemBasedExpenseLineDetail?.Qty}
                          onChange={(e) =>
                            updateLineItem(
                              index,
                              "Qty",
                              Number.parseFloat(e.target.value) || 0,
                            )
                          }
                        />
                      </div>

                      <div>
                        <Label htmlFor={`price-${index}`}>Unit Price</Label>
                        <Input
                          id={`price-${index}`}
                          type="number"
                          step="0.01"
                          value={line?.ItemBasedExpenseLineDetail?.UnitPrice}
                          onChange={(e) =>
                            updateLineItem(
                              index,
                              "UnitPrice",
                              Number.parseFloat(e.target.value) || 0,
                            )
                          }
                        />
                      </div>

                      <div>
                        <Label htmlFor={`tax-${index}`}>Tax</Label>
                        <Select
                          value={
                            line?.ItemBasedExpenseLineDetail?.TaxCodeRef?.value
                          }
                          onValueChange={(value) =>
                            updateLineItem(index, "TaxCodeRef", { value })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {ocrResponse?.taxes?.map((tax) => (
                              <SelectItem key={tax.id} value={tax.id}>
                                {tax.name} ({tax.rate}%)
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Amount</Label>
                        <Input
                          value={line?.Amount?.toFixed(2)}
                          readOnly
                          className="bg-muted"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Private Notes</Label>
            <Textarea
              id="notes"
              value={billData?.PrivateNote}
              onChange={(e) =>
                setBillData({ ...billData, PrivateNote: e.target.value })
              }
              className="min-h-[80px]"
            />
          </div>

          {/* Total */}
          <div className="flex justify-end">
            <div className="space-y-2 text-right">
              <div className="text-lg font-semibold">
                Total Amount: ${getTotalAmount()?.toFixed(2)}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col gap-3 pt-4 sm:flex-row">
            <Button onClick={handleSubmit} className="flex-1">
              Create Bill in QuickBooks
            </Button>
            <Button variant="outline" className="flex-1 bg-transparent">
              Save as Draft
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
