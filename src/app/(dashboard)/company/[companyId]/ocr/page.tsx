"use client";
import type React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FileText, Trash2, Upload, Lightbulb, Loader } from "lucide-react";
import { GoogleGenAI, Type } from "@google/genai";
import { api } from "@/trpc/react";
import { useParams } from "next/navigation";
import BillValidationForm from "./components/bill-validation-form";

export default function BillUpload() {
  // const params = useParams() as { companyId: string };
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const params = useParams() as { companyId: string };
  const [base64, setBase64] = useState<string | null>(null);
  const { mutateAsync: createBill, data } =
    api.quickbooks.createBillOcr.useMutation();
  const {
    mutateAsync: OCRBill,
    data: ocrBillData,
    isPending: OCRLoading,
  } = api.ocr.ocr.useMutation();
  const { mutateAsync: addImage } = api.ocr.createAttachable.useMutation();
  const { mutateAsync: getRelevantVendors } =
    api.ocr.searchVendors.useMutation();
  useEffect(() => {
    console.log(data);
  }, [data]);
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    if (selectedFile.type.startsWith("image/")) {
      // preview part
      const fileUrl = URL.createObjectURL(selectedFile);
      setFile(selectedFile);
      setPreview(fileUrl);
      // base64 part
      const reader = new FileReader();
      reader.readAsDataURL(selectedFile);
      reader.onloadend = () => {
        const result = reader.result as string;
        setBase64(result);
        console.log("Base64:", result);
      };
    } else {
      alert("Please select an image");
      setBase64(null); // Not an image
    }
  };

  const handleDelete = () => {
    setFile(null);
    setPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };
  function extractMimeTypeAndBase64(dataUri: string) {
    try {
      const matches = dataUri.match(
        /^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+);base64,(.*)$/,
      );

      if (!matches || matches.length !== 3) {
        return { mimeType: null, base64: null }; // Or throw an error, depending on your needs.
      }

      const mimeType = matches[1];
      const base64 = matches[2];

      return { mimeType: mimeType, base64: base64 };
    } catch (error) {
      console.error("Error extracting mime type and base64:", error);
      return { mimeType: null, base64: null }; // Or re-throw the error if appropriate.
    }
  }

  const handleUpload = async () => {
    if (!OCRLoading)
      await OCRBill({
        companyId: params.companyId,
        base64: base64 || "",
      });
    // console.log(billRes);
  };

  return (
    <>
      {ocrBillData ? (
        <BillValidationForm ocrResponse={ocrBillData as any} />
      ) : (
        <div className="p-3">
          <Card className="mx-auto max-w-md p-6">
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">
                  Upload Bill to QuickBooks
                </h3>
                <p className="text-muted-foreground text-sm">
                  Upload a bill or take a picture to quickly add it to your
                  QuickBooks account.
                </p>
              </div>

              <div className="space-y-4">
                <div className="space-y-3">
                  <Label
                    htmlFor="picture"
                    className="block text-base font-medium"
                  >
                    Upload Your Bill
                  </Label>
                  <div className="relative">
                    <Input
                      id="picture"
                      type="file"
                      ref={fileInputRef}
                      onChange={handleFileInput}
                      accept="image/png, image/jpeg, image/jpg, application/pdf"
                      className="sr-only absolute"
                    />
                    <div
                      onClick={() => fileInputRef.current?.click()}
                      className="flex cursor-pointer items-center justify-center gap-2 rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 px-4 py-3 transition-colors hover:bg-gray-100"
                    >
                      <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                        <Upload className="text-primary h-5 w-5" />
                      </div>
                      <div className="text-left">
                        <p className="text-sm font-medium">Click to upload</p>
                        <p className="text-muted-foreground hidden text-xs md:block">
                          or drag and drop
                        </p>
                        <p className="text-muted-foreground block text-xs md:hidden">
                          or take a picture
                        </p>
                      </div>
                    </div>
                    {file && (
                      <p className="text-muted-foreground mt-2 text-sm">
                        Selected:{" "}
                        <span className="text-foreground font-medium">
                          {file.name}
                        </span>
                      </p>
                    )}
                  </div>
                </div>

                <div className="text-muted-foreground space-y-2 text-sm">
                  <p className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Accepted formats: JPG, PNG, PDF
                  </p>
                  <p className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    Maximum file size: 10MB
                  </p>
                </div>
                <Accordion type="single" collapsible>
                  <AccordionItem value="item-1">
                    <AccordionTrigger>
                      <div className="flex items-start gap-2">
                        <Lightbulb className="mt-0.5 h-5 w-5 flex-shrink-0 text-amber-500" />
                        <div className="space-y-1">
                          <p className="text-sm font-medium text-amber-800">
                            Tips for successful bill processing
                          </p>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <ul className="list-disc space-y-1 pl-4 text-xs text-amber-700">
                        <li>
                          Ensure the image is clear, well-lit, and not blurry
                        </li>
                        <li>Capture the entire bill with all edges visible</li>
                        <li>
                          Make sure the date, amount, and vendor name are
                          clearly visible
                        </li>
                        <li>Flatten crumpled bills before taking a photo</li>
                        <li>Avoid shadows or glare on the document</li>
                        <li>
                          For best results, place the bill on a dark,
                          contrasting background
                        </li>
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>

                {preview && (
                  <div className="space-y-3">
                    <div className="text-sm font-medium">Preview</div>
                    <div className="relative h-32 w-32 overflow-hidden rounded-md border">
                      {file?.type.includes("image") ? (
                        <Image
                          src={preview || "/placeholder.svg"}
                          alt="Bill preview"
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="bg-muted flex h-full items-center justify-center">
                          <FileText className="text-muted-foreground h-10 w-10" />
                        </div>
                      )}
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-1 right-1 h-6 w-6"
                        onClick={handleDelete}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="max-w-[200px] truncate text-sm">
                      {file?.name}
                    </div>
                  </div>
                )}

                {base64 && (
                  <Button
                    disabled={OCRLoading}
                    onClick={handleUpload}
                    className="w-full"
                  >
                    {OCRLoading ? (
                      <Loader className="animate-spin" />
                    ) : (
                      "Process Bill"
                    )}
                  </Button>
                )}
              </div>
            </div>
          </Card>
        </div>
      )}
    </>
  );
}
